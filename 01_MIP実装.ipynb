{"cells": [{"cell_type": "markdown", "id": "cbcec2c1", "metadata": {}, "source": ["## 日々スケジューリングの定式化\n", "\n", "### 定数\n", "各品番$i \\quad i = 1, \\dots, m$  \n", "各期間$t \\quad t = 1, \\dots, n$  \n", "品番$i$の単位在庫コスト$v_{i}$  \n", "品番$i$の単位製造コスト$c_{i}$  \n", "品番$i$の単位段取り替えコスト$s_{i}$  \n", "品番$i$の単位機会損失コスト$h_{i}$  \n", "期間$t$，品番$i$の機会損失量$g_{it}$  \n", "期間$t$，品番$i$の需要量$d_{it}$  \n", "期間$t$，品番$i$の単位売上$r_{it}$  \n", "品番$i$を1単位生産するための必要時間$a_{i}$  \n", "品番$i$へ段取り替えするための必要時間$b_{i}$  \n", "最大負荷時間$T$  \n", "最大段取り替え回数$U$  \n", "最小生産数を表す定数$\\epsilon$  \n", "最大生産数を表す定数$\\zeta$  \n", "\n", "### 決定変数\n", "期間$t$，品番$i$へ段取り替えするときに1を取る2値変数$x_{it} \\in {0,1}$  \n", "期間$t$，品番$i$の生産量$p_{it}$  \n", "期間$t$，品番$i$の在庫量$I_{it} = I_{i(t-1)} + p_{it} - d_{it}$  \n", "\n", "### 目的関数\n", "$\\max \\sum_{i=1}^m \\sum_{t=1}^n (d_{it}r_{it} - \\{v_{i}I_{it} + c_{i}p_{it} + s_{i}x_{it} + h_{i}g_{it} \\})$  \n", "\n", "\n", "### 制約条件\n", "(1) 総生産時間と総段取り替え時間は最大負荷時間以下  \n", "(2) 総段取り替え回数は最大段取り替え回数以下  \n", "(3) 在庫量の式  \n", "(4) 機会損失量の式  \n", "(5) 生産量は非負  \n", "(6) 段取り替えは0, 1\n", "\n", "\n", "$\\sum_{i=1}^m a_{i}p_{it} + \\sum_{i=1}^m b_{i}x_{it} \\leq T \\quad \\forall t$  \n", "$\\sum_{i=1}^m x_{it} \\leq U \\quad \\forall t$  \n", "$I_{it} = I_{i(t-1)} + p_{it} - d_{it} \\quad \\forall i, t$  \n", "$g_{it} = \\max \\{0, d_{it} - I_{it} \\} \\quad \\forall i, t$  \n", "$\\epsilon x_{it} \\leq p_{it} \\leq \\zeta x_{it} \\quad \\forall i, t$  \n", "$p_{it} \\geq 0 \\quad \\forall i, t$  \n", "$x_{it} \\in {0,1} \\quad \\forall i, t$  "]}, {"cell_type": "code", "execution_count": 1, "id": "404fd61f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Welcome to the CBC MILP Solver \n", "Version: 2.10.3 \n", "Build Date: Dec 15 2019 \n", "\n", "command line - /opt/anaconda3/lib/python3.12/site-packages/pulp/apis/../solverdir/cbc/osx/i64/cbc /var/folders/_p/gpx29nx95pn__7720hyny3sm0000gn/T/e2178e95cacb4478ba442d34799d01e3-pulp.mps -max -timeMode elapsed -branch -printingOptions all -solution /var/folders/_p/gpx29nx95pn__7720hyny3sm0000gn/T/e2178e95cacb4478ba442d34799d01e3-pulp.sol (default strategy 1)\n", "At line 2 NAME          MODEL\n", "At line 3 ROWS\n", "At line 109 COLUMNS\n", "At line 536 RHS\n", "At line 641 BOUNDS\n", "At line 666 ENDATA\n", "Problem MODEL has 104 rows, 96 columns and 282 elements\n", "Coin0008I MODEL read with 0 errors\n", "Option for timeMode changed from cpu to elapsed\n", "Continuous objective value is -19443 - 0.00 seconds\n", "Cgl0003I 0 fixed, 0 tightened bounds, 4 strengthened rows, 0 substitutions\n", "Cgl0004I processed model has 62 rows, 60 columns (22 integer (22 of which binary)) and 172 elements\n", "Cbc0038I Initial state - 6 integers unsatisfied sum - 0.575984\n", "Cbc0038I Pass   1: suminf.    0.00000 (0) obj. 37820.6 iterations 8\n", "Cbc0038I Solution found of 37820.6\n", "Cbc0038I Relaxing continuous gives 20697.4\n", "Cbc0038I Before mini branch and bound, 16 integers at bound fixed and 22 continuous\n", "Cbc0038I Full problem 62 rows 60 columns, reduced to 18 rows 16 columns\n", "Cbc0038I Mini branch and bound improved solution from 20697.4 to 19750.8 (0.02 seconds)\n", "Cbc0038I Freeing continuous variables gives a solution of 19750.8\n", "Cbc0038I Round again with cutoff of 19733\n", "Cbc0038I Pass   2: suminf.    0.40704 (6) obj. 19733 iterations 10\n", "Cbc0038I Pass   3: suminf.    0.52498 (2) obj. 19733 iterations 7\n", "Cbc0038I Pass   4: suminf.    0.52498 (2) obj. 19733 iterations 0\n", "Cbc0038I Pass   5: suminf.    0.30754 (2) obj. 19733 iterations 1\n", "Cbc0038I Pass   6: suminf.    0.50734 (3) obj. 19733 iterations 5\n", "Cbc0038I Pass   7: suminf.    0.50734 (3) obj. 19733 iterations 2\n", "Cbc0038I Pass   8: suminf.    0.50734 (3) obj. 19733 iterations 3\n", "Cbc0038I Pass   9: suminf.    0.50734 (3) obj. 19733 iterations 3\n", "Cbc0038I Pass  10: suminf.    0.52498 (2) obj. 19733 iterations 5\n", "Cbc0038I Pass  11: suminf.    0.52498 (2) obj. 19733 iterations 0\n", "Cbc0038I Pass  12: suminf.    0.30754 (2) obj. 19733 iterations 1\n", "Cbc0038I Pass  13: suminf.    0.25802 (3) obj. 19733 iterations 7\n", "Cbc0038I Pass  14: suminf.    0.19530 (2) obj. 19733 iterations 7\n", "Cbc0038I Pass  15: suminf.    0.57960 (4) obj. 19733 iterations 5\n", "Cbc0038I Pass  16: suminf.    0.57960 (4) obj. 19733 iterations 1\n", "Cbc0038I Pass  17: suminf.    0.52498 (2) obj. 19733 iterations 3\n", "Cbc0038I Pass  18: suminf.    0.52498 (2) obj. 19733 iterations 0\n", "Cbc0038I Pass  19: suminf.    0.30754 (2) obj. 19733 iterations 1\n", "Cbc0038I Pass  20: suminf.    0.30754 (2) obj. 19733 iterations 1\n", "Cbc0038I Pass  21: suminf.    0.30754 (2) obj. 19733 iterations 1\n", "Cbc0038I Pass  22: suminf.    0.30754 (2) obj. 19733 iterations 1\n", "Cbc0038I Pass  23: suminf.    0.85548 (5) obj. 19733 iterations 9\n", "Cbc0038I Pass  24: suminf.    0.32621 (3) obj. 19733 iterations 5\n", "Cbc0038I Pass  25: suminf.    0.30754 (2) obj. 19733 iterations 6\n", "Cbc0038I Pass  26: suminf.    0.62758 (3) obj. 19733 iterations 5\n", "Cbc0038I Pass  27: suminf.    0.11838 (1) obj. 19733 iterations 6\n", "Cbc0038I Pass  28: suminf.    0.65925 (5) obj. 19733 iterations 9\n", "Cbc0038I Pass  29: suminf.    0.51464 (4) obj. 19733 iterations 5\n", "Cbc0038I Pass  30: suminf.    0.52498 (2) obj. 19733 iterations 4\n", "Cbc0038I Pass  31: suminf.    0.52498 (2) obj. 19733 iterations 0\n", "Cbc0038I No solution found this major pass\n", "Cbc0038I Before mini branch and bound, 11 integers at bound fixed and 19 continuous\n", "Cbc0038I Full problem 62 rows 60 columns, reduced to 32 rows 28 columns\n", "Cbc0038I Mini branch and bound did not improve solution (0.03 seconds)\n", "Cbc0038I After 0.03 seconds - Feasibility pump exiting with objective of 19750.8 - took 0.01 seconds\n", "Cbc0012I Integer solution of 19750.833 found by feasibility pump after 0 iterations and 0 nodes (0.03 seconds)\n", "Cbc0038I Full problem 62 rows 60 columns, reduced to 20 rows 22 columns\n", "Cbc0031I 9 added rows had average density of 5.7777778\n", "Cbc0013I At root node, 16 cuts changed objective from 19572.5 to 19750.833 in 2 passes\n", "Cbc0014I Cut generator 0 (Probing) - 9 row cuts average 2.1 elements, 0 column cuts (0 active)  in 0.000 seconds - new frequency is 1\n", "Cbc0014I Cut generator 1 (Gomory) - 9 row cuts average 8.6 elements, 0 column cuts (0 active)  in 0.000 seconds - new frequency is 1\n", "Cbc0014I Cut generator 2 (Knapsack) - 0 row cuts average 0.0 elements, 0 column cuts (0 active)  in 0.000 seconds - new frequency is -100\n", "Cbc0014I Cut generator 3 (Clique) - 0 row cuts average 0.0 elements, 0 column cuts (0 active)  in 0.000 seconds - new frequency is -100\n", "Cbc0014I Cut generator 4 (MixedIntegerRounding2) - 0 row cuts average 0.0 elements, 0 column cuts (0 active)  in 0.000 seconds - new frequency is -100\n", "Cbc0014I Cut generator 5 (FlowCover) - 0 row cuts average 0.0 elements, 0 column cuts (0 active)  in 0.000 seconds - new frequency is -100\n", "Cbc0014I Cut generator 6 (TwoMirCuts) - 9 row cuts average 6.6 elements, 0 column cuts (0 active)  in 0.000 seconds - new frequency is 1\n", "Cbc0001I Search completed - best objective 19750.8330626228, took 20 iterations and 0 nodes (0.03 seconds)\n", "Cbc0035I Maximum depth 0, 16 variables fixed on reduced cost\n", "Cuts at root node changed objective from 19572.5 to 19750.8\n", "Probing was tried 2 times and created 9 cuts of which 0 were active after adding rounds of cuts (0.000 seconds)\n", "<PERSON><PERSON><PERSON> was tried 2 times and created 9 cuts of which 0 were active after adding rounds of cuts (0.000 seconds)\n", "K<PERSON>sa<PERSON> was tried 2 times and created 0 cuts of which 0 were active after adding rounds of cuts (0.000 seconds)\n", "<PERSON><PERSON> was tried 2 times and created 0 cuts of which 0 were active after adding rounds of cuts (0.000 seconds)\n", "MixedIntegerRounding2 was tried 2 times and created 0 cuts of which 0 were active after adding rounds of cuts (0.000 seconds)\n", "FlowCover was tried 2 times and created 0 cuts of which 0 were active after adding rounds of cuts (0.000 seconds)\n", "TwoMirCuts was tried 2 times and created 9 cuts of which 0 were active after adding rounds of cuts (0.000 seconds)\n", "ZeroHalf was tried 1 times and created 0 cuts of which 0 were active after adding rounds of cuts (0.000 seconds)\n", "\n", "Result - Optimal solution found\n", "\n", "Objective value:                -19750.83306262\n", "Enumerated nodes:               0\n", "Total iterations:               20\n", "Time (CPU seconds):             0.01\n", "Time (Wallclock seconds):       0.03\n", "\n", "Option for printingOptions changed from normal to all\n", "Total time (CPU seconds):       0.02   (Wallclock seconds):       0.04\n", "\n", "Optimal objective value: 9520.17\n", "\n", "Setup Schedule:\n", "           Period_1  Period_2  Period_3  Period_4\n", "Product_1       1.0       1.0       1.0       1.0\n", "Product_2       0.0       0.0       0.0       0.0\n", "Product_3       1.0       0.0       1.0       0.0\n", "Product_4       0.0       0.0       0.0       0.0\n", "Product_5       0.0       0.0       0.0       0.0\n", "Product_6       0.0       0.0       0.0       0.0\n", "\n", "Production Schedule:\n", "             Period_1   Period_2    Period_3   Period_4\n", "Product_1  291.471550  201.98478  128.912020  195.36564\n", "Product_2    0.000000    0.00000    0.000000    0.00000\n", "Product_3   65.393649    0.00000   50.342176    0.00000\n", "Product_4    0.000000    0.00000    0.000000    0.00000\n", "Product_5    0.000000    0.00000    0.000000    0.00000\n", "Product_6    0.000000    0.00000    0.000000    0.00000\n", "\n", "Inventory Levels:\n", "              Period_1     Period_2    Period_3    Period_4\n", "Product_1   145.735780   173.860280  151.386150  173.375900\n", "Product_2  1133.656600  1064.450200  993.357660  918.055390\n", "Product_3    44.860118    22.430059   51.460645   25.730323\n", "Product_4   388.445220   378.068910  365.687310  351.881600\n", "Product_5   373.479560   352.405000  329.872560  303.592820\n", "Product_6   597.108510   595.001640  592.239080  589.993560\n"]}], "source": ["from mip_production_scheduler import load_data_from_csv\n", "scheduler = load_data_from_csv(\"data_ga.csv\")\n", "status = scheduler.solve()\n", "scheduler.print_solution()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}