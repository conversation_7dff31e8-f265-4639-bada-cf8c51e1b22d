"""
ベースコード
"""

import random
import numpy as np
import pandas as pd
import csv
from deap import base, creator, tools, algorithms
import matplotlib.pyplot as plt
import japanize_matplotlib

# グローバル変数
品番リスト = []
出荷数リスト = []
収容数リスト = []
サイクルタイムリスト = []
込め数リスト = []
初期在庫量リスト = []
品番数 = 0
期間 = 25

def read_csv(file_path):
    """CSVファイルを読み込む関数"""
    global 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト
    
    with open(file_path, 'r', encoding='utf-8') as file:
        reader = csv.reader(file)
        header = next(reader)
        
        品番リスト = []
        出荷数リスト = []
        収容数リスト = []
        サイクルタイムリスト = []
        込め数リスト = []
        初期在庫量リスト = []
        
        for row in reader:
            if len(row) == 0:
                continue
            品番リスト.append(row[header.index("part_number")])
            出荷数リスト.append(int(row[header.index("shipment")]))
            収容数リスト.append(int(row[header.index("capacity")]))
            サイクルタイムリスト.append(float(row[header.index("cycle_time")])/60)
            込め数リスト.append(int(row[header.index("cabity")]))
            初期在庫量リスト.append(int(row[header.index("initial_inventory")]))
        
        return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト

def evaluate(ind):
    """評価関数"""
    global 品番数, 期間
    
    total_setup_penalty = 0
    total_overtime_penalty = 0
    total_shortage_penalty = 0
    
    inventory = 初期在庫量リスト[:]
    max_daily_time = (8 + 2) * 60 * 2  # 1日の最大稼働時間（分）
    
    for t in range(期間):
        daily_time = 0
        daily_setup = 0
        
        for i in range(品番数):
            idx = t * 品番数 + i
            production = ind[idx]
            
            # 在庫が十分にある場合は生産しない
            if inventory[i] >= 出荷数リスト[i]:
                production = 0
            
            if production > 0:
                setup_time = 30
                daily_setup += 1
            else:
                setup_time = 0
            
            if サイクルタイムリスト[i] > 0 and 込め数リスト[i] > 0:
                production_time = production / (サイクルタイムリスト[i] * 込め数リスト[i])
                daily_time += production_time + setup_time
            
            inventory[i] += production - 出荷数リスト[i]
            
            # 出荷遅れペナルティ
            if inventory[i] < 0:
                shortage_amount = abs(inventory[i])
                delay_penalty = 50000 * shortage_amount
                total_shortage_penalty += delay_penalty
                inventory[i] = 0
        
        # 稼働時間超過ペナルティ
        if daily_time > max_daily_time:
            overtime = daily_time - max_daily_time
            total_overtime_penalty += 1000000 * overtime
        
        # 段替え制約ペナルティ
        if daily_setup > 5:
            total_setup_penalty += 10000 * (daily_setup - 5)
    
    total_penalty = total_setup_penalty + total_overtime_penalty + total_shortage_penalty
    return total_penalty,

def generate_ind():
    """個体生成関数"""
    productions = []
    temp_inventory = 初期在庫量リスト[:]
    max_daily_time = (8 + 2) * 60 * 2
    
    for t in range(期間):
        daily_time = 0
        daily_productions = [0] * 品番数
        
        # 優先度を計算（在庫不足の度合いで優先順位決定）
        priorities = []
        for i in range(品番数):
            shortage = max(0, 出荷数リスト[i] - temp_inventory[i])
            risk = shortage * 出荷数リスト[i]  # 不足量 × 需要量
            priorities.append((risk, i))
        
        priorities.sort(reverse=True)
        for risk, i in priorities:
            shortage = max(0, 出荷数リスト[i] - temp_inventory[i])
            
            if shortage > 0:
                setup_time = 30
                remaining_time = max_daily_time - daily_time - setup_time
                
                if remaining_time > 0 and サイクルタイムリスト[i] > 0 and 込め数リスト[i] > 0:
                    cycle_time_per_unit = サイクルタイムリスト[i] * 込め数リスト[i]
                    max_producible = int(remaining_time / cycle_time_per_unit)
                    
                    if max_producible > 0:
                        # 不足分と時間制約の範囲内で生産量を決定
                        target_production = min(shortage * 10, max_producible)  # 不足分の最大10倍まで
                        production = random.randint(shortage, max(shortage, target_production))
                        production_time = setup_time + production * cycle_time_per_unit
                        
                        # 稼働時間制約厳守
                        if daily_time + production_time <= max_daily_time:
                            daily_productions[i] = production
                            daily_time += production_time
        
        for i in range(品番数):
            productions.append(daily_productions[i])
            temp_inventory[i] += daily_productions[i] - 出荷数リスト[i]
            temp_inventory[i] = max(0, temp_inventory[i])
    
    return creator.Individual(productions)

def mutate(ind):
    """突然変異関数（時間制約対応版）"""
    max_daily_time = (8 + 2) * 60 * 2
    
    for t in range(期間):
        if random.random() < 0.1:  # 10%の確率で期間全体を変更
            for i in range(品番数):
                if random.random() < 0.3:
                    idx = t * 品番数 + i
                    
                    # 現在の生産量を基準に変更
                    current_production = ind[idx]
                    change = random.randint(-100, 100)
                    new_production = max(0, current_production + change)
                    new_production = min(2000, new_production)  # 上限制限
                    
                    ind[idx] = new_production
    
    return ind,

def plot_results(best_individual):
    """結果をプロットする関数"""
    global 品番数, 期間, 品番リスト, 出荷数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト

    print("\n=== 結果のプロット ===")
    
    total_inventory_per_period = []
    total_production_time_per_period = []
    total_setup_times_per_period = []
    total_shipment_delay_per_period = []

    inventory = 初期在庫量リスト[:]
    max_daily_time = (8 + 2) * 60 * 2
    
    for t in range(期間):
        daily_inventory = 0
        daily_production_time = 0
        daily_setup_times = 0
        daily_shipment_delay = 0

        for i in range(品番数):
            idx = t * 品番数 + i
            production = best_individual[idx]

            # 在庫が十分にある場合は生産しない
            if inventory[i] >= 出荷数リスト[i]:
                production = 0

            if production > 0:
                daily_setup_times += 1
                setup_time = 30
            else:
                setup_time = 0

            # 生産時間の計算
            if サイクルタイムリスト[i] > 0 and 込め数リスト[i] > 0:
                production_time = production / (サイクルタイムリスト[i] * 込め数リスト[i])
                daily_production_time += production_time + setup_time

            # 在庫更新と出荷遅れ計算
            inventory[i] += production - 出荷数リスト[i]

            # 出荷遅れの計算
            if inventory[i] < 0:
                daily_shipment_delay += abs(inventory[i])
                inventory[i] = 0

            daily_inventory += inventory[i]

        total_inventory_per_period.append(daily_inventory)
        total_production_time_per_period.append(daily_production_time)
        total_setup_times_per_period.append(daily_setup_times)
        total_shipment_delay_per_period.append(daily_shipment_delay)

    # プロット作成（2x2レイアウト）
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))

    periods = list(range(1, 期間 + 1))

    # 1. 各期間の総在庫量
    axes[0, 0].bar(periods, total_inventory_per_period, color='skyblue', alpha=0.7)
    axes[0, 0].set_title('各期間の総在庫量', fontweight='bold')
    axes[0, 0].set_xlabel('期間')
    axes[0, 0].set_ylabel('総在庫量 (個)')
    axes[0, 0].grid(True, alpha=0.3)
    axes[0, 0].set_ylim(0, 3000)

    # 2. 各期間の総生産時間（制限ラインを追加）
    bars_time = axes[0, 1].bar(periods, total_production_time_per_period, color='lightgreen', alpha=0.7)
    axes[0, 1].axhline(y=max_daily_time, color='red', linestyle='--', alpha=0.8,
                       label=f'上限 ({max_daily_time}分)')
    axes[0, 1].set_title('各期間の総生産時間', fontweight='bold')
    axes[0, 1].set_xlabel('期間')
    axes[0, 1].set_ylabel('総稼働時間 (分)')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    axes[0, 1].set_ylim(0, 1500)

    # 3. 各期間の総段替え回数
    axes[1, 0].bar(periods, total_setup_times_per_period, color='salmon', alpha=0.7)
    axes[1, 0].axhline(y=5, color='red', linestyle='--', alpha=0.8, label='上限 (5回)')
    axes[1, 0].set_title('各期間の総段替え回数', fontweight='bold')
    axes[1, 0].set_xlabel('期間')
    axes[1, 0].set_ylabel('総段替え回数（回）')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    axes[1, 0].set_ylim(0, 6)

    # 4. 各期間の総出荷遅れ量
    bars_delay = axes[1, 1].bar(periods, total_shipment_delay_per_period, color='gold', alpha=0.7)
    axes[1, 1].set_title('各期間の総出荷遅れ量', fontweight='bold')
    axes[1, 1].set_xlabel('期間')
    axes[1, 1].set_ylabel('総出荷遅れ量 (個)')
    axes[1, 1].grid(True, alpha=0.3)
    axes[1, 1].set_ylim(0, 1000)

    plt.tight_layout()
    plt.show()

    # 統計情報を出力
    delay_violations = sum(1 for x in total_shipment_delay_per_period if x > 0)
    time_violations = sum(1 for x in total_production_time_per_period if x > max_daily_time)
    setup_violations = sum(1 for x in total_setup_times_per_period if x > 5)

    print(f"時間制約違反: {time_violations} 期間")
    print(f"段替え制約違反: {setup_violations} 期間")
    print(f"出荷遅れ制約違反: {delay_violations} 期間")

    return total_inventory_per_period, total_production_time_per_period, total_setup_times_per_period, total_shipment_delay_per_period

def main():
    """メイン実行関数"""
    global 品番数, 期間

    # CSVファイルの読み込み
    result = read_csv('data_ga.csv')
    if result[0] is None:
        print("CSVファイルの読み込みに失敗しました")
        return

    品番数 = len(品番リスト)
    期間 = 20

    # DEAP設定
    if hasattr(creator, 'FitnessMin'):
        del creator.FitnessMin
    if hasattr(creator, 'Individual'):
        del creator.Individual

    creator.create("FitnessMin", base.Fitness, weights=(-1.0,))
    creator.create("Individual", list, fitness=creator.FitnessMin)

    toolbox = base.Toolbox()
    toolbox.register("individual", generate_ind)
    toolbox.register("population", tools.initRepeat, list, toolbox.individual)
    toolbox.register("evaluate", evaluate)
    toolbox.register("mate", tools.cxTwoPoint)
    toolbox.register("mutate", mutate)
    toolbox.register("select", tools.selTournament, tournsize=3)

    # GAパラメータ（遅れ削減重視）
    population_size = 150  # 集団サイズを増加
    generations = 100      # 世代数を増加
    cxpb = 0.6
    mutpb = 0.3           # 突然変異率を増加

    # 初期集団生成
    population = toolbox.population(n=population_size)
    hof = tools.HallOfFame(1)
    stats = tools.Statistics(lambda ind: ind.fitness.values)
    stats.register("avg", np.mean)
    stats.register("min", np.min)
    stats.register("max", np.max)

    # GA実行
    population, logbook = algorithms.eaSimple(
        population, toolbox,
        cxpb=cxpb, mutpb=mutpb, ngen=generations,
        stats=stats, halloffame=hof, verbose=True
    )

    # 結果の表示
    best_ind = hof[0]
    best_fitness = best_ind.fitness.values[0]

    print(f"\n=== 最適化結果 ===")
    print(f"最良個体のペナルティ: {best_fitness:.2f}")

    # 結果をプロット
    inventory_data, production_time_data, setup_data, shipment_delay_data = plot_results(best_ind)

    return best_ind, logbook

if __name__ == "__main__":
    print("=== GA生産スケジューリング最適化システム ===")
    best_solution, log = main()

