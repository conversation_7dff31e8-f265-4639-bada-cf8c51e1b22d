"""
Daily Production Scheduling MIP Implementation using PuLP
Based on mathematical formulation from 01_MIP実装.ipynb
"""

import pulp
import numpy as np
import pandas as pd


class ProductionScheduler:
    """Daily Production Scheduling MIP Solver using PuLP"""
    
    def __init__(self, m, n, v_i, c_i, s_i, h_i, d_it, r_it, a_i, b_i, T, U,
                 epsilon=1.0, zeta=1000.0, initial_inventory=None):
        self.m, self.n = m, n
        self.v_i, self.c_i, self.s_i, self.h_i = v_i, c_i, s_i, h_i
        self.d_it, self.r_it = d_it, r_it
        self.a_i, self.b_i = a_i, b_i
        self.T, self.U = T, U
        self.epsilon, self.zeta = epsilon, zeta
        self.initial_inventory = initial_inventory or [0.0] * m
        self.prob = None
        self.x_it = self.p_it = self.I_it = self.g_it = {}
        
    def build_model(self):
        """Build the MIP model"""
        self.prob = pulp.LpProblem("Production_Scheduling", pulp.LpMaximize)

        # Decision variables
        indices = [(i, t) for i in range(self.m) for t in range(self.n)]
        self.x_it = pulp.LpVariable.dicts("setup", indices, cat='Binary')
        self.p_it = pulp.LpVariable.dicts("production", indices, lowBound=0)
        self.I_it = pulp.LpVariable.dicts("inventory", indices, lowBound=0)
        self.g_it = pulp.LpVariable.dicts("opportunity_loss", indices, lowBound=0)

        # Objective: maximize profit
        self.prob += (pulp.lpSum([self.d_it[i][t] * self.r_it[i][t] for i in range(self.m) for t in range(self.n)]) -
                     pulp.lpSum([self.v_i[i] * self.I_it[(i, t)] + self.c_i[i] * self.p_it[(i, t)] +
                                self.s_i[i] * self.x_it[(i, t)] + self.h_i[i] * self.g_it[(i, t)]
                                for i in range(self.m) for t in range(self.n)]))
        self._add_constraints()
        
    def _add_constraints(self):
        """Add all constraints to the model"""
        for t in range(self.n):
            # (1) Production + setup time <= max load time
            self.prob += pulp.lpSum([self.a_i[i] * self.p_it[(i, t)] + self.b_i[i] * self.x_it[(i, t)]
                                    for i in range(self.m)]) <= self.T
            # (2) Number of setups <= max setups
            self.prob += pulp.lpSum([self.x_it[(i, t)] for i in range(self.m)]) <= self.U

        for i in range(self.m):
            for t in range(self.n):
                # (3) Inventory balance
                prev_inv = self.initial_inventory[i] if t == 0 else self.I_it[(i, t-1)]
                self.prob += self.I_it[(i, t)] == prev_inv + self.p_it[(i, t)] - self.d_it[i][t]
                # (4) Opportunity loss
                self.prob += self.g_it[(i, t)] >= self.d_it[i][t] - self.I_it[(i, t)]
                # (5) Production bounds
                self.prob += self.epsilon * self.x_it[(i, t)] <= self.p_it[(i, t)]
                self.prob += self.p_it[(i, t)] <= self.zeta * self.x_it[(i, t)]
    
    def solve(self, solver=None, time_limit=None, verbose=True):
        """Solve the MIP problem"""
        if self.prob is None:
            self.build_model()
        if solver is None:
            solver = pulp.PULP_CBC_CMD(msg=verbose, timeLimit=time_limit)
        self.prob.solve(solver)
        return pulp.LpStatus[self.prob.status]
    
    def get_solution(self):
        """Extract solution from solved model"""
        if self.prob.status != pulp.LpStatusOptimal:
            return {"status": "Not optimal", "objective": None}

        solution = {"status": "Optimal", "objective_value": pulp.value(self.prob.objective),
                   "setup_schedule": np.zeros((self.m, self.n)), "production_schedule": np.zeros((self.m, self.n)),
                   "inventory_levels": np.zeros((self.m, self.n)), "opportunity_losses": np.zeros((self.m, self.n))}

        for i in range(self.m):
            for t in range(self.n):
                solution["setup_schedule"][i][t] = pulp.value(self.x_it[(i, t)])
                solution["production_schedule"][i][t] = pulp.value(self.p_it[(i, t)])
                solution["inventory_levels"][i][t] = pulp.value(self.I_it[(i, t)])
                solution["opportunity_losses"][i][t] = pulp.value(self.g_it[(i, t)])
        return solution
    
    def print_solution(self):
        """Print formatted solution"""
        solution = self.get_solution()
        if solution["status"] != "Optimal":
            print(f"Solution status: {solution['status']}")
            return

        periods = [f"Period_{t+1}" for t in range(self.n)]
        products = [f"Product_{i+1}" for i in range(self.m)]

        print(f"Optimal objective value: {solution['objective_value']:.2f}")
        print("\nSetup Schedule:")
        print(pd.DataFrame(solution["setup_schedule"], columns=periods, index=products))
        print("\nProduction Schedule:")
        print(pd.DataFrame(solution["production_schedule"], columns=periods, index=products))
        print("\nInventory Levels:")
        print(pd.DataFrame(solution["inventory_levels"], columns=periods, index=products))

    def print_input_data(self, product_names=None):
        """Print input data for verification"""
        if product_names is None:
            product_names = [f"Product_{i+1}" for i in range(self.m)]

        print("=== Input Data ===")
        print(f"Products: {self.m}, Periods: {self.n}, Max time: {self.T}, Max setups: {self.U}")

        print("\nCost Parameters:")
        print(pd.DataFrame({'Product': product_names, 'Inventory_Cost': self.v_i, 'Production_Cost': self.c_i,
                           'Setup_Cost': self.s_i, 'Opportunity_Cost': self.h_i, 'Production_Time': self.a_i,
                           'Setup_Time': self.b_i, 'Initial_Inventory': self.initial_inventory}))

        periods = [f"Period_{t+1}" for t in range(self.n)]
        print("\nDemand Matrix:")
        print(pd.DataFrame(self.d_it, columns=periods, index=product_names))
        print("\nRevenue Matrix:")
        print(pd.DataFrame(self.r_it, columns=periods, index=product_names))


def load_data_from_csv(csv_file="data_ga.csv", n_periods=4, unit_inventory_cost=1.0,
                      unit_production_cost=10.0, unit_setup_cost=50.0, unit_opportunity_cost=100.0,
                      unit_revenue=25.0, setup_time=30.0, max_load_time=1200.0, max_setups=6):
    """Load production data from CSV file and create ProductionScheduler"""
    df = pd.read_csv(csv_file)
    m, n = len(df), n_periods

    shipments = df['shipment'].tolist()
    cycle_times = df['cycle_time'].tolist()
    initial_inventories = df['initial_inventory'].tolist()

    # Convert cycle times from seconds to minutes and create cost arrays
    a_i = [ct / 60.0 for ct in cycle_times]
    v_i = [unit_inventory_cost] * m
    c_i = [unit_production_cost] * m
    s_i = [unit_setup_cost] * m
    h_i = [unit_opportunity_cost] * m
    b_i = [setup_time] * m

    # Create demand matrix with variation
    d_it = np.zeros((m, n))
    for i in range(m):
        base_demand = shipments[i] / n
        for t in range(n):
            d_it[i][t] = max(0, base_demand * (0.8 + 0.4 * np.random.random()))

    r_it = np.full((m, n), unit_revenue)

    return ProductionScheduler(m, n, v_i, c_i, s_i, h_i, d_it, r_it, a_i, b_i,
                              max_load_time, max_setups, initial_inventory=initial_inventories)


def create_sample_problem():
    """Create a sample problem for testing"""
    m, n = 3, 4
    v_i, c_i, s_i, h_i = [1.0, 1.5, 2.0], [10.0, 15.0, 20.0], [50.0, 60.0, 70.0], [100.0, 120.0, 150.0]
    d_it = np.array([[10, 15, 20, 12], [8, 12, 18, 10], [5, 8, 15, 7]])
    r_it = np.array([[25, 25, 25, 25], [30, 30, 30, 30], [35, 35, 35, 35]])
    a_i, b_i = [2.0, 3.0, 4.0], [30.0, 40.0, 50.0]
    return ProductionScheduler(m, n, v_i, c_i, s_i, h_i, d_it, r_it, a_i, b_i, 200.0, 2)


if __name__ == "__main__":
    print("=== Production Scheduling with CSV Data ===")
    try:
        scheduler = load_data_from_csv()
        product_names = pd.read_csv('data_ga.csv')['part_number'].tolist()
        print(f"Loaded data for {scheduler.m} products and {scheduler.n} periods")
        scheduler.print_input_data(product_names)

        print("\n" + "="*50 + "\nSOLVING MIP PROBLEM...\n" + "="*50)
        status = scheduler.solve()
        print(f"\nSolution status: {status}")

        if status == "Optimal":
            print("\n" + "="*50 + "\nOPTIMAL SOLUTION FOUND\n" + "="*50)
            scheduler.print_solution()
        else:
            print("Could not find optimal solution")
    except FileNotFoundError:
        print("data_ga.csv not found. Using sample problem instead.")
        scheduler = create_sample_problem()
        status = scheduler.solve()
        print(f"Solution status: {status}")
        scheduler.print_solution()
