from pulp import *

# --- 1. データの定義 (課題のデータに基づいて定義) ---

# セット
PRODUCTS = ['製品A', '製品B', '製品C']  # 製品 [cite: 126]
INTERMEDIATES = ['中間製品1', '中間製品2', '中間製品3']  # 中間製品 [cite: 126]
MACHINES_INTERMEDIATE = ['M1', 'M2', 'M3', 'M4', 'M5', 'M6']  # 中間製品加工機 [cite: 109, 126]
MACHINES_ASSEMBLY = ['M7']  # 組み立て機 [cite: 109, 126]
ALL_MACHINES = MACHINES_INTERMEDIATE + MACHINES_ASSEMBLY
ALL_ITEMS = PRODUCTS + INTERMEDIATES

# パラメーター
# ロットサイズ (個数/ロット) [cite: 126]
lot_size = {item: 10 for item in ALL_ITEMS}

# 加工時間 (分) [cite: 126]
# 中間製品の加工時間
proc_time_intermediate = {
    ('中間製品1', 'M1'): 4, ('中間製品1', 'M2'): 5, ('中間製品1', 'M3'): 4,
    ('中間製品1', 'M4'): 5, ('中間製品1', 'M5'): 6, ('中間製品1', 'M6'): 3,
    ('中間製品2', 'M1'): 5, ('中間製品2', 'M2'): 5, ('中間製品2', 'M3'): 4,
    ('中間製品2', 'M4'): 3, ('中間製品2', 'M5'): 3, ('中間製品2', 'M6'): 3,
    ('中間製品3', 'M1'): 4, ('中間製品3', 'M2'): 5, ('中間製品3', 'M3'): 6,
    ('中間製品3', 'M4'): 4, ('中間製品3', 'M5'): 6, ('中間製品3', 'M6'): 6
}
# 製品の組み立て時間 (M7) [cite: 126]
proc_time_assembly = {
    ('製品A', 'M7'): 12,
    ('製品B', 'M7'): 14,
    ('製品C', 'M7'): 16
}

# 全ての作業時間 [cite: 126]
processing_time = {
    (item, machine): proc_time_intermediate.get((item, machine), 0)
    for item in INTERMEDIATES for machine in MACHINES_INTERMEDIATE
}
processing_time.update({
    (item, machine): proc_time_assembly.get((item, machine), 0)
    for item in PRODUCTS for machine in MACHINES_ASSEMBLY
})

# 計画条件（生産量とロット数） [cite: 133]
plan_conditions = [
    {'product': '製品A', 'start_time': 0, 'quantity': 50},
    {'product': '製品B', 'start_time': 0, 'quantity': 60},
    {'product': '製品C', 'start_time': 0, 'quantity': 40},
    {'product': '製品A', 'start_time': 120, 'quantity': 40},
    {'product': '製品B', 'start_time': 120, 'quantity': 50},
    {'product': '製品C', 'start_time': 120, 'quantity': 60},
    {'product': '製品A', 'start_time': 240, 'quantity': 50},
    {'product': '製品B', 'start_time': 240, 'quantity': 40},
    {'product': '製品C', 'start_time': 240, 'quantity': 40},
]

# ロットごとのジョブを定義 [cite: 128]
jobs = []
for cond in plan_conditions:
    num_lots = cond['quantity'] // lot_size[cond['product']]
    for i in range(num_lots):
        job_id = f"{cond['product']}_lot{i+1}_{cond['start_time']}"
        jobs.append({'id': job_id, 'product': cond['product'], 'start_time': cond['start_time']})

# 切り替え時間 [cite: 135]
setup_time = {
    ('中間製品1', '中間製品2'): 8, ('中間製品1', '中間製品3'): 7,
    ('中間製品2', '中間製品1'): 8, ('中間製品2', '中間製品3'): 9,
    ('中間製品3', '中間製品1'): 7, ('中間製品3', '中間製品2'): 9,
}
# 切り替え時間がない場合を0で定義
for i in INTERMEDIATES:
    for j in INTERMEDIATES:
        if (i, j) not in setup_time and i != j:
            setup_time[(i, j)] = 0

# --- 2. モデルの構築 ---

# 最適化問題のインスタンスを作成
prob = LpProblem("Production_Scheduling", LpMinimize)

# --- 3. 変数の定義 ---

# 各ジョブの開始時刻と完了時刻 [cite: 130]
start_time = LpVariable.dicts("S", (job['id'] for job in jobs), 0, None, LpContinuous)
completion_time = LpVariable.dicts("C", (job['id'] for job in jobs), 0, None, LpContinuous)

# 機械ごとの作業順序を示すバイナリ変数 [cite: 32]
order = LpVariable.dicts("X", [(job1['id'], job2['id'], m) for job1 in jobs for job2 in jobs if job1['id'] != job2['id'] for m in ALL_MACHINES], 0, 1, LpBinary)

# メイクスパン
makespan = LpVariable("Makespan", 0, None, LpContinuous)

# --- 4. 目的関数の定義 ---

# メイクスパンの最小化 [cite: 32]
prob += makespan, "Minimizing Makespan"

# --- 5. 制約条件の追加 ---

# 作業開始・完了時刻の関係 [cite: 130]
for job in jobs:
    job_id = job['id']
    product = job['product']
    # 中間製品のロットを考慮
    intermediate_proc_time = [
        processing_time.get((inter, m), 0)
        for inter in INTERMEDIATES
        for m in MACHINES_INTERMEDIATE
    ]
    # 最終製品の組み立てを考慮
    assembly_proc_time = processing_time.get((product, 'M7'), 0)
    
    # ここでは単純化のため、各ジョブの総作業時間を仮定
    total_job_time = sum(intermediate_proc_time) / len(intermediate_proc_time) + assembly_proc_time
    prob += completion_time[job_id] >= start_time[job_id] + total_job_time, f"CompletionTime_{job_id}"

# 機械能力制約 (Big-M法)
BigM = 10000
for m in ALL_MACHINES:
    for i in range(len(jobs)):
        for k in range(i + 1, len(jobs)):
            job1_id = jobs[i]['id']
            job2_id = jobs[k]['id']
            
            # 各ジョブの製品名を取得して切り替え時間を適用
            item1 = jobs[i]['product']
            item2 = jobs[k]['product']
            st = setup_time.get((item1, item2), 0)
            
            prob += start_time[job1_id] >= completion_time[job2_id] + st - BigM * order[job1_id, job2_id, m], f"MachineOrder1_{job1_id}_{job2_id}_{m}"
            prob += start_time[job2_id] >= completion_time[job1_id] + st - BigM * (1 - order[job1_id, job2_id, m]), f"MachineOrder2_{job1_id}_{job2_id}_{m}"

# メイクスパンの定義 [cite: 32]
for job in jobs:
    prob += makespan >= completion_time[job['id']], f"MakespanConstraint_{job['id']}"
    
# --- 6. 求解 ---

# GLPKソルバーを指定して問題を解く [cite: 9]
prob.solve(GLPK())

# --- 7. 結果の表示 ---

print("Status:", LpStatus[prob.status])
print("Optimal Makespan =", value(prob.objective))

# 実行可能なジョブのスケジュールを表示
for job in jobs:
    job_id = job['id']
    print(f"Job: {job_id}, Start: {value(start_time[job_id])}, End: {value(completion_time[job_id])}")

pip install GLPK
