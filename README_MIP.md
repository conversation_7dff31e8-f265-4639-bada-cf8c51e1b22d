# MIP Production Scheduling Implementation

This repository contains a Python implementation of the Mixed Integer Programming (MIP) production scheduling problem using PuLP, based on the mathematical formulation from `01_MIP実装.ipynb`.

## Files

- `mip_production_scheduler.py` - Main implementation with ProductionScheduler class
- `run_mip_example.py` - Simple example script
- `data_ga.csv` - Input data file
- `README_MIP.md` - This documentation

## Mathematical Formulation

The implementation solves the following optimization problem:

**Objective:** Maximize profit = Revenue - (Inventory Cost + Production Cost + Setup Cost + Opportunity Loss Cost)

**Decision Variables:**
- `x_it`: Binary setup variable (1 if product i is set up in period t)
- `p_it`: Production quantity of product i in period t
- `I_it`: Inventory level of product i at end of period t
- `g_it`: Opportunity loss for product i in period t

**Constraints:**
1. Production time + Setup time ≤ Maximum load time per period
2. Number of setups ≤ Maximum setups per period
3. Inventory balance: I_it = I_i(t-1) + p_it - d_it
4. Opportunity loss: g_it ≥ d_it - I_it
5. Production bounds: ε·x_it ≤ p_it ≤ ζ·x_it

## CSV Data Format

The `data_ga.csv` file should contain the following columns:

```csv
part_number,shipment,capacity,cycle_time,cabity,initial_inventory
A,600,80,36,2,0
B,300,80,32,2,1200
...
```

Where:
- `part_number`: Product identifier
- `shipment`: Total demand across all periods
- `capacity`: Production capacity per period (currently not used in constraints)
- `cycle_time`: Production time per unit (seconds)
- `cabity`: Additional parameter (currently not used)
- `initial_inventory`: Starting inventory level

## Usage

### Basic Usage

```python
from mip_production_scheduler import load_data_from_csv

# Load data and create scheduler
scheduler = load_data_from_csv("data_ga.csv")

# Solve the problem
status = scheduler.solve()

if status == "Optimal":
    scheduler.print_solution()
```

### Advanced Usage with Custom Parameters

```python
scheduler = load_data_from_csv(
    csv_file="data_ga.csv",
    n_periods=4,                    # Number of time periods
    unit_inventory_cost=1.0,        # Cost per unit inventory per period
    unit_production_cost=10.0,      # Cost per unit production
    unit_setup_cost=50.0,           # Cost per setup
    unit_opportunity_cost=100.0,    # Cost per unit opportunity loss
    unit_revenue=25.0,              # Revenue per unit sold
    setup_time=30.0,                # Setup time (minutes)
    max_load_time=1200.0,           # Max production time per period (minutes)
    max_setups=6                    # Max number of setups per period
)
```

### Running the Example

```bash
# Run the main implementation
python mip_production_scheduler.py

# Run the simple example
python run_mip_example.py
```

## Key Features

1. **Flexible Parameter Configuration**: All cost parameters and constraints are configurable
2. **CSV Data Integration**: Automatically loads production data from CSV files
3. **Comprehensive Output**: Shows setup schedules, production schedules, and inventory levels
4. **Solution Analysis**: Provides objective value and summary statistics
5. **Error Handling**: Graceful handling of infeasible or unbounded problems

## Example Output

```
=== Production Scheduling with CSV Data ===
Loaded data for 6 products and 4 periods

=== Input Data ===
Number of products: 6
Number of periods: 4
Maximum load time per period: 1200.0
Maximum setups per period: 6

Cost Parameters:
  Product  Inventory_Cost  Production_Cost  Setup_Cost  ...
0       A             1.0             10.0        50.0  ...
1       B             1.0             10.0        50.0  ...
...

Solution status: Optimal
Optimal objective value: 9522.55

Setup Schedule:
           Period_1  Period_2  Period_3  Period_4
Product_1       1.0       1.0       1.0       1.0
Product_2       0.0       0.0       0.0       0.0
...
```

## Dependencies

- `pulp` - Linear programming library
- `pandas` - Data manipulation
- `numpy` - Numerical computations

Install dependencies:
```bash
pip install pulp pandas numpy
```

## Notes

- The demand matrix is automatically generated by distributing total shipment across periods with random variation
- Revenue is assumed constant across all products and periods (can be customized)
- Production time is converted from seconds (CSV) to minutes (model)
- The model uses a daily scheduling approach with configurable time limits
